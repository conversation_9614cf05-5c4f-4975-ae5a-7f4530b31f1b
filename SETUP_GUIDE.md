# FastAPI Cognito Auth - Quick Setup Guide

## 🚀 Quick Start

### 1. Prerequisites Setup

**AWS Cognito User Pool Configuration:**

1. Go to AWS Cognito Console
2. Create a new User Pool:
   - Pool name: `fastapi-auth-pool`
   - Sign-in options: Username, Email
   - Password policy: Default or custom
   - MFA: Optional (recommended for production)

3. Create App Client:
   - App client name: `fastapi-client`
   - Authentication flows: 
     - ✅ ALLOW_ADMIN_USER_PASSWORD_AUTH
     - ✅ ALLOW_REFRESH_TOKEN_AUTH
     - ✅ ALLOW_USER_SRP_AUTH
   - Generate client secret: ✅ Yes (recommended)

4. Note down these values:
   - User Pool ID: `us-east-1_XXXXXXXXX`
   - App Client ID: `xxxxxxxxxxxxxxxxxxxxxxxxxx`
   - App Client Secret: `xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`

### 2. Environment Setup

```bash
# Copy environment template
cp .env.example .env

# Edit with your values
nano .env
```

**Required .env content:**
```env
AWS_REGION=us-east-1
COGNITO_USER_POOL_ID=us-east-1_XXXXXXXXX
COGNITO_CLIENT_ID=xxxxxxxxxxxxxxxxxxxxxxxxxx
COGNITO_CLIENT_SECRET=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
DEBUG=true
```

### 3. Install Dependencies

```bash
# Install Python dependencies
pip install -r requirements.txt

# For deployment (optional)
npm install
```

### 4. Create Test User

In AWS Cognito Console:
1. Go to your User Pool → Users
2. Click "Create user"
3. Set username: `testuser`
4. Set temporary password: `TempPass123!`
5. Uncheck "Send an invitation to this new user"
6. Check "Mark phone number as verified" (if using phone)
7. Check "Mark email as verified" (if using email)

### 5. Run the Application

```bash
# Start the server
python3 main.py

# Or using uvicorn
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 6. Test the API

**Option 1: Using the test script**
```bash
# Test with credentials
python3 test_api.py testuser TempPass123!

# Test without authentication
python3 test_api.py
```

**Option 2: Using curl**
```bash
# Test login
curl -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser", "password": "TempPass123!"}'

# Test protected route (replace TOKEN with actual token)
curl -X GET "http://localhost:8000/protected/profile" \
  -H "Authorization: Bearer TOKEN"
```

**Option 3: Using Swagger UI**
- Open: http://localhost:8000/docs
- Try the `/auth/login` endpoint
- Copy the access_token
- Click "Authorize" and paste the token
- Test protected endpoints

## 🌐 API Endpoints Overview

### Public Endpoints
- `GET /` - API information
- `GET /health` - Health check
- `GET /ping` - Simple ping
- `GET /docs` - Swagger documentation

### Authentication Endpoints
- `POST /auth/login` - User login
- `POST /auth/refresh` - Refresh token
- `POST /auth/logout` - User logout (requires auth)
- `GET /auth/me` - Current user info (requires auth)
- `POST /auth/validate` - Validate token (requires auth)

### Protected Endpoints
- `GET /protected/profile` - User profile (requires auth)
- `GET /protected/dashboard` - User dashboard (requires auth)
- `GET /protected/public-with-optional-auth` - Optional auth
- `GET /protected/admin` - Admin route (requires auth)

## 🚀 Deployment Options

### Option 1: AWS Lambda + API Gateway (Serverless)

```bash
# Set environment variables
export COGNITO_USER_POOL_ID=your_pool_id
export COGNITO_CLIENT_ID=your_client_id
export COGNITO_CLIENT_SECRET=your_client_secret

# Deploy
./deploy.sh

# Or manually
serverless deploy
```

### Option 2: Docker Container

```bash
# Build image
docker build -t fastapi-cognito-auth .

# Run container
docker run -p 8000:8000 \
  -e COGNITO_USER_POOL_ID=your_pool_id \
  -e COGNITO_CLIENT_ID=your_client_id \
  -e COGNITO_CLIENT_SECRET=your_client_secret \
  fastapi-cognito-auth
```

### Option 3: Traditional Server

```bash
# Install dependencies
pip install -r requirements.txt

# Set environment variables
export COGNITO_USER_POOL_ID=your_pool_id
export COGNITO_CLIENT_ID=your_client_id
export COGNITO_CLIENT_SECRET=your_client_secret

# Run with gunicorn (production)
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000
```

## 🔧 Troubleshooting

### Common Issues

1. **"Invalid JWT token"**
   - Check Cognito configuration
   - Verify token hasn't expired
   - Ensure correct User Pool ID and Client ID

2. **"Authentication failed"**
   - Verify username/password
   - Check if user exists in Cognito
   - Ensure user is confirmed

3. **"CORS errors"**
   - Update `ALLOWED_ORIGINS` in config.py
   - Check frontend domain configuration

4. **"Module not found"**
   - Install dependencies: `pip install -r requirements.txt`
   - Check Python path

### Debug Mode

Enable detailed error messages:
```env
DEBUG=true
```

### Logs

Check application logs for detailed error information:
```bash
# Local development
python3 main.py

# Docker
docker logs container_name

# AWS Lambda
serverless logs -f api
```

## 📚 Next Steps

1. **Frontend Integration**: Use the JWT tokens in your frontend application
2. **User Management**: Implement user registration, password reset
3. **Role-Based Access**: Add user roles and permissions
4. **Rate Limiting**: Implement API rate limiting
5. **Monitoring**: Add logging and monitoring
6. **Testing**: Write comprehensive tests

## 🔒 Security Best Practices

1. **Never commit .env files**
2. **Use HTTPS in production**
3. **Implement proper CORS**
4. **Set up rate limiting**
5. **Monitor authentication attempts**
6. **Regularly rotate secrets**
7. **Use least privilege IAM policies**

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section
2. Review AWS Cognito documentation
3. Check FastAPI documentation
4. Enable debug mode for detailed errors
