{"name": "fastapi-cognito-auth", "version": "1.0.0", "description": "FastAPI with AWS Cognito authentication and API Gateway", "scripts": {"deploy": "serverless deploy", "deploy:prod": "serverless deploy --stage prod", "remove": "serverless remove", "info": "serverless info", "logs": "serverless logs -f api"}, "devDependencies": {"serverless": "^3.38.0", "serverless-python-requirements": "^6.0.0"}, "keywords": ["<PERSON><PERSON><PERSON>", "aws", "cognito", "api-gateway", "serverless", "authentication", "jwt"], "author": "Your Name", "license": "MIT"}