#!/bin/bash

# FastAPI Cognito Auth Deployment Script

set -e

echo "🚀 FastAPI Cognito Auth Deployment Script"
echo "=========================================="

# Check if required environment variables are set
check_env_vars() {
    echo "🔍 Checking environment variables..."
    
    if [ -z "$COGNITO_USER_POOL_ID" ]; then
        echo "❌ COGNITO_USER_POOL_ID environment variable is not set"
        exit 1
    fi
    
    if [ -z "$COGNITO_CLIENT_ID" ]; then
        echo "❌ COGNITO_CLIENT_ID environment variable is not set"
        exit 1
    fi
    
    echo "✅ Environment variables are set"
}

# Install dependencies
install_dependencies() {
    echo "📦 Installing dependencies..."
    
    # Install Node.js dependencies
    if [ -f "package.json" ]; then
        echo "Installing Node.js dependencies..."
        npm install
    fi
    
    # Install Python dependencies
    if [ -f "requirements.txt" ]; then
        echo "Installing Python dependencies..."
        pip install -r requirements.txt
    fi
    
    echo "✅ Dependencies installed"
}

# Deploy to AWS
deploy() {
    local stage=${1:-dev}
    echo "🚀 Deploying to AWS (stage: $stage)..."
    
    # Deploy using Serverless Framework
    if [ "$stage" = "prod" ]; then
        serverless deploy --stage prod
    else
        serverless deploy --stage dev
    fi
    
    echo "✅ Deployment completed"
}

# Get deployment info
get_info() {
    echo "📋 Getting deployment information..."
    serverless info
}

# Main deployment flow
main() {
    local stage=${1:-dev}
    
    echo "Starting deployment for stage: $stage"
    echo ""
    
    check_env_vars
    echo ""
    
    install_dependencies
    echo ""
    
    deploy $stage
    echo ""
    
    get_info
    echo ""
    
    echo "🎉 Deployment completed successfully!"
    echo ""
    echo "📝 Next steps:"
    echo "1. Test your API endpoints"
    echo "2. Create test users in Cognito User Pool"
    echo "3. Update your frontend to use the new API URL"
}

# Help function
show_help() {
    echo "Usage: $0 [stage]"
    echo ""
    echo "Arguments:"
    echo "  stage    Deployment stage (dev|prod) [default: dev]"
    echo ""
    echo "Environment variables required:"
    echo "  COGNITO_USER_POOL_ID    AWS Cognito User Pool ID"
    echo "  COGNITO_CLIENT_ID       AWS Cognito Client ID"
    echo "  COGNITO_CLIENT_SECRET   AWS Cognito Client Secret (optional)"
    echo ""
    echo "Examples:"
    echo "  $0              # Deploy to dev stage"
    echo "  $0 dev          # Deploy to dev stage"
    echo "  $0 prod         # Deploy to prod stage"
}

# Parse command line arguments
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    ""|dev|prod)
        main "$1"
        ;;
    *)
        echo "❌ Invalid stage: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
