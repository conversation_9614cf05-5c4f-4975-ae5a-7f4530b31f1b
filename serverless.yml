service: fastapi-cognito-auth

frameworkVersion: '3'

provider:
  name: aws
  runtime: python3.9
  region: ${opt:region, 'us-east-1'}
  stage: ${opt:stage, 'dev'}
  environment:
    AWS_REGION: ${self:provider.region}
    COGNITO_USER_POOL_ID: ${env:COGNITO_USER_POOL_ID}
    COGNITO_CLIENT_ID: ${env:COGNITO_CLIENT_ID}
    COGNITO_CLIENT_SECRET: ${env:COGNITO_CLIENT_SECRET}
    DEBUG: false
  
  # IAM role statements
  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - cognito-idp:AdminInitiateAuth
            - cognito-idp:AdminGetUser
            - cognito-idp:GetUser
            - cognito-idp:GlobalSignOut
          Resource: 
            - arn:aws:cognito-idp:${self:provider.region}:*:userpool/${env:COGNITO_USER_POOL_ID}

functions:
  api:
    handler: lambda_handler.handler
    events:
      - httpApi:
          path: /{proxy+}
          method: ANY
      - httpApi:
          path: /
          method: ANY
    timeout: 30
    memorySize: 512

plugins:
  - serverless-python-requirements

custom:
  pythonRequirements:
    dockerizePip: true
    slim: true
    strip: false

package:
  patterns:
    - '!node_modules/**'
    - '!.git/**'
    - '!.pytest_cache/**'
    - '!tests/**'
    - '!.env'
    - '!README.md'
