from pydantic import BaseModel, EmailStr
from typing import Optional, Dict, Any
from datetime import datetime


class User(BaseModel):
    user_id: str
    username: str
    email: Optional[EmailStr] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone_number: Optional[str] = None
    email_verified: bool = False
    phone_verified: bool = False
    enabled: bool = True
    user_status: str
    created_date: Optional[datetime] = None
    last_modified_date: Optional[datetime] = None
    attributes: Optional[Dict[str, Any]] = None


class UserProfile(BaseModel):
    user_id: str
    username: str
    email: Optional[EmailStr] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone_number: Optional[str] = None


class CreateUserRequest(BaseModel):
    username: str
    password: str
    email: EmailStr
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone_number: Optional[str] = None
