from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import uvicorn

from config import settings
from routers import auth, protected


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    print("🚀 FastAPI application starting up...")
    print(f"📍 AWS Region: {settings.aws_region}")
    print(f"🔐 Cognito User Pool ID: {settings.cognito_user_pool_id}")
    print(f"🆔 Cognito Client ID: {settings.cognito_client_id}")
    
    yield
    
    # Shutdown
    print("🛑 FastAPI application shutting down...")


# Create FastAPI application
app = FastAPI(
    title=settings.api_title,
    version=settings.api_version,
    description="FastAPI application with AWS Cognito authentication and API Gateway integration",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=settings.allowed_methods,
    allow_headers=settings.allowed_headers,
)


# Global exception handler
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": "HTTPException",
            "message": exc.detail,
            "status_code": exc.status_code
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content={
            "error": "InternalServerError",
            "message": "An unexpected error occurred",
            "status_code": 500
        }
    )


# Include routers
app.include_router(auth.router)
app.include_router(protected.router)


# Root endpoint
@app.get("/")
async def root():
    return {
        "message": "FastAPI with AWS Cognito Authentication",
        "version": settings.api_version,
        "docs": "/docs",
        "redoc": "/redoc",
        "health": "/health"
    }


# Health check endpoint
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "fastapi-cognito-auth",
        "version": settings.api_version
    }


# API Gateway health check (for ALB target groups)
@app.get("/ping")
async def ping():
    return {"message": "pong"}


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level="info" if not settings.debug else "debug"
    )
