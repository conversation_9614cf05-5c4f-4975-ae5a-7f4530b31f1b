from fastapi import <PERSON><PERSON>out<PERSON>, HTTPException, status, Depends
from fastapi.security import HTTPAuthorizationCredentials
from auth.cognito import cognito_auth
from auth.dependencies import security, get_current_user
from models.auth import (
    LoginRequest, 
    LoginResponse, 
    RefreshTokenRequest, 
    LogoutRequest,
    TokenValidationResponse,
    ErrorResponse
)
from models.user import UserProfile


router = APIRouter(prefix="/auth", tags=["Authentication"])


@router.post("/login", response_model=LoginResponse)
async def login(login_request: LoginRequest):
    """
    Authenticate user with AWS Cognito and return JWT tokens
    """
    result = cognito_auth.authenticate_user(
        username=login_request.username,
        password=login_request.password
    )
    
    if not result['success']:
        error_code = result.get('error_code', 'AuthenticationFailed')
        error_message = result.get('error_message', 'Authentication failed')
        
        # Map Cognito error codes to HTTP status codes
        status_code = status.HTTP_401_UNAUTHORIZED
        if error_code in ['UserNotFoundException', 'NotAuthorizedException']:
            status_code = status.HTTP_401_UNAUTHORIZED
        elif error_code == 'UserNotConfirmedException':
            status_code = status.HTTP_403_FORBIDDEN
        elif error_code == 'TooManyRequestsException':
            status_code = status.HTTP_429_TOO_MANY_REQUESTS
        
        raise HTTPException(
            status_code=status_code,
            detail={
                "error": error_code,
                "message": error_message
            }
        )
    
    tokens = result['tokens']
    
    return LoginResponse(
        access_token=tokens['AccessToken'],
        token_type="bearer",
        expires_in=tokens['ExpiresIn'],
        refresh_token=tokens.get('RefreshToken')
    )


@router.post("/refresh", response_model=LoginResponse)
async def refresh_token(refresh_request: RefreshTokenRequest):
    """
    Refresh access token using refresh token
    """
    # Note: We need the username for refresh token. In a real app, you might
    # store this in a database or extract it from the refresh token
    # For now, we'll extract it from the refresh token claims
    try:
        from jose import jwt
        # Decode without verification to get username
        claims = jwt.get_unverified_claims(refresh_request.refresh_token)
        username = claims.get('cognito:username')
        
        if not username:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid refresh token"
            )
        
        result = cognito_auth.refresh_token(
            refresh_token=refresh_request.refresh_token,
            username=username
        )
        
        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={
                    "error": result.get('error_code', 'TokenRefreshFailed'),
                    "message": result.get('error_message', 'Token refresh failed')
                }
            )
        
        tokens = result['tokens']
        
        return LoginResponse(
            access_token=tokens['AccessToken'],
            token_type="bearer",
            expires_in=tokens['ExpiresIn'],
            refresh_token=tokens.get('RefreshToken')
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid refresh token"
        )


@router.post("/logout")
async def logout(
    current_user: UserProfile = Depends(get_current_user),
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Logout user by invalidating tokens
    """
    access_token = credentials.credentials
    
    result = cognito_auth.logout_user(access_token)
    
    if not result['success']:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "error": result.get('error_code', 'LogoutFailed'),
                "message": result.get('error_message', 'Logout failed')
            }
        )
    
    return {"message": "Successfully logged out"}


@router.get("/me", response_model=UserProfile)
async def get_current_user_info(current_user: UserProfile = Depends(get_current_user)):
    """
    Get current user information
    """
    return current_user


@router.post("/validate", response_model=TokenValidationResponse)
async def validate_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """
    Validate JWT token and return user information
    """
    token = credentials.credentials
    
    verification_result = cognito_auth.verify_jwt_token(token)
    
    if verification_result['valid']:
        return TokenValidationResponse(
            valid=True,
            user_id=verification_result.get('user_id'),
            username=verification_result.get('username'),
            email=verification_result.get('email')
        )
    else:
        return TokenValidationResponse(
            valid=False,
            error=verification_result.get('error', 'Invalid token')
        )
