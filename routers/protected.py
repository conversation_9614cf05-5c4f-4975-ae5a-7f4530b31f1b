from fastapi import APIRouter, Depends
from auth.dependencies import get_current_user, get_optional_user
from models.user import UserProfile
from typing import Optional


router = APIRouter(prefix="/protected", tags=["Protected Routes"])


@router.get("/profile")
async def get_user_profile(current_user: UserProfile = Depends(get_current_user)):
    """
    Get user profile - requires authentication
    """
    return {
        "message": "This is a protected route",
        "user": current_user,
        "access_level": "authenticated"
    }


@router.get("/dashboard")
async def get_dashboard(current_user: UserProfile = Depends(get_current_user)):
    """
    Get user dashboard - requires authentication
    """
    return {
        "message": f"Welcome to your dashboard, {current_user.username}!",
        "user_id": current_user.user_id,
        "email": current_user.email,
        "dashboard_data": {
            "last_login": "2024-01-01T00:00:00Z",
            "notifications": 5,
            "pending_tasks": 3
        }
    }


@router.get("/public-with-optional-auth")
async def public_with_optional_auth(current_user: Optional[UserProfile] = Depends(get_optional_user)):
    """
    Public route that shows different content based on authentication status
    """
    if current_user:
        return {
            "message": f"Hello {current_user.username}, you are logged in!",
            "authenticated": True,
            "user": current_user
        }
    else:
        return {
            "message": "Hello anonymous user!",
            "authenticated": False,
            "suggestion": "Login to see personalized content"
        }


@router.get("/admin")
async def admin_only(current_user: UserProfile = Depends(get_current_user)):
    """
    Admin only route - requires authentication and admin role
    Note: This is a simple example. In a real app, you would check user roles/groups
    """
    # In a real application, you would check if the user has admin role
    # For now, we'll just return admin content for any authenticated user
    return {
        "message": "Admin dashboard",
        "user": current_user,
        "admin_data": {
            "total_users": 1000,
            "active_sessions": 150,
            "system_status": "healthy"
        }
    }
