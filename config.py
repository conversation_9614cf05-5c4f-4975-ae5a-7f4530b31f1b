from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    # AWS Cognito Configuration
    aws_region: str = "us-east-1"
    cognito_user_pool_id: str
    cognito_client_id: str
    cognito_client_secret: Optional[str] = None
    
    # JWT Configuration
    jwt_algorithm: str = "RS256"
    jwt_audience: Optional[str] = None
    
    # API Configuration
    api_title: str = "FastAPI with Cognito Auth"
    api_version: str = "1.0.0"
    debug: bool = False
    
    # CORS Configuration
    allowed_origins: list[str] = ["*"]
    allowed_methods: list[str] = ["*"]
    allowed_headers: list[str] = ["*"]
    
    class Config:
        env_file = ".env"
        case_sensitive = False


settings = Settings()
