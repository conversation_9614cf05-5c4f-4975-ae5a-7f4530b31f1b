#!/usr/bin/env python3
"""
Simple test script to verify the FastAPI Cognito authentication
"""

import requests
import json
import sys
from typing import Optional


class APITester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.access_token: Optional[str] = None
        self.refresh_token: Optional[str] = None
    
    def test_health_check(self):
        """Test health check endpoint"""
        print("🔍 Testing health check...")
        try:
            response = requests.get(f"{self.base_url}/health")
            print(f"✅ Health check: {response.status_code}")
            print(f"   Response: {response.json()}")
            return response.status_code == 200
        except Exception as e:
            print(f"❌ Health check failed: {e}")
            return False
    
    def test_root_endpoint(self):
        """Test root endpoint"""
        print("\n🔍 Testing root endpoint...")
        try:
            response = requests.get(f"{self.base_url}/")
            print(f"✅ Root endpoint: {response.status_code}")
            print(f"   Response: {response.json()}")
            return response.status_code == 200
        except Exception as e:
            print(f"❌ Root endpoint failed: {e}")
            return False
    
    def test_login(self, username: str, password: str):
        """Test user login"""
        print(f"\n🔍 Testing login for user: {username}")
        try:
            payload = {
                "username": username,
                "password": password
            }
            response = requests.post(
                f"{self.base_url}/auth/login",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                self.access_token = data.get("access_token")
                self.refresh_token = data.get("refresh_token")
                print(f"✅ Login successful: {response.status_code}")
                print(f"   Token type: {data.get('token_type')}")
                print(f"   Expires in: {data.get('expires_in')} seconds")
                print(f"   Access token: {self.access_token[:50]}...")
                return True
            else:
                print(f"❌ Login failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Login failed: {e}")
            return False
    
    def test_protected_route(self):
        """Test protected route with authentication"""
        if not self.access_token:
            print("❌ No access token available for protected route test")
            return False
        
        print("\n🔍 Testing protected route...")
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            response = requests.get(f"{self.base_url}/protected/profile", headers=headers)
            
            if response.status_code == 200:
                print(f"✅ Protected route access: {response.status_code}")
                print(f"   Response: {response.json()}")
                return True
            else:
                print(f"❌ Protected route failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Protected route failed: {e}")
            return False
    
    def test_user_info(self):
        """Test get current user info"""
        if not self.access_token:
            print("❌ No access token available for user info test")
            return False
        
        print("\n🔍 Testing user info endpoint...")
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            response = requests.get(f"{self.base_url}/auth/me", headers=headers)
            
            if response.status_code == 200:
                print(f"✅ User info: {response.status_code}")
                print(f"   Response: {response.json()}")
                return True
            else:
                print(f"❌ User info failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ User info failed: {e}")
            return False
    
    def test_token_validation(self):
        """Test token validation"""
        if not self.access_token:
            print("❌ No access token available for token validation test")
            return False
        
        print("\n🔍 Testing token validation...")
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            response = requests.post(f"{self.base_url}/auth/validate", headers=headers)
            
            if response.status_code == 200:
                print(f"✅ Token validation: {response.status_code}")
                print(f"   Response: {response.json()}")
                return True
            else:
                print(f"❌ Token validation failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Token validation failed: {e}")
            return False
    
    def run_all_tests(self, username: str = None, password: str = None):
        """Run all tests"""
        print("🚀 Starting API tests...\n")
        
        results = []
        
        # Test public endpoints
        results.append(self.test_health_check())
        results.append(self.test_root_endpoint())
        
        # Test authentication if credentials provided
        if username and password:
            login_success = self.test_login(username, password)
            results.append(login_success)
            
            if login_success:
                results.append(self.test_protected_route())
                results.append(self.test_user_info())
                results.append(self.test_token_validation())
        else:
            print("\n⚠️  No credentials provided, skipping authentication tests")
            print("   Usage: python test_api.py <username> <password>")
        
        # Summary
        passed = sum(results)
        total = len(results)
        
        print(f"\n📊 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed!")
            return True
        else:
            print("❌ Some tests failed")
            return False


if __name__ == "__main__":
    # Get credentials from command line arguments
    username = sys.argv[1] if len(sys.argv) > 1 else None
    password = sys.argv[2] if len(sys.argv) > 2 else None
    
    # Run tests
    tester = APITester()
    success = tester.run_all_tests(username, password)
    
    sys.exit(0 if success else 1)
