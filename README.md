# FastAPI with AWS Cognito Authentication and API Gateway

A complete FastAPI application with AWS Cognito authentication, JWT token validation, and API Gateway integration.

## Features

- 🔐 AWS Cognito User Pool authentication
- 🎫 JWT token validation and verification
- 🛡️ Protected routes with authentication middleware
- 🚀 API Gateway integration with Serverless Framework
- 📝 Comprehensive error handling
- 🔄 Token refresh functionality
- 👤 User profile management
- 🌐 CORS support
- 📚 Interactive API documentation (Swagger/OpenAPI)

## Project Structure

```
├── auth/
│   ├── __init__.py
│   ├── cognito.py          # AWS Cognito client and authentication logic
│   └── dependencies.py     # FastAPI authentication dependencies
├── models/
│   ├── __init__.py
│   ├── auth.py            # Authentication request/response models
│   └── user.py            # User data models
├── routers/
│   ├── __init__.py
│   ├── auth.py            # Authentication endpoints
│   └── protected.py       # Protected routes
├── main.py                # FastAPI application
├── lambda_handler.py      # AWS Lambda handler
├── config.py              # Configuration settings
├── requirements.txt       # Python dependencies
├── serverless.yml         # Serverless Framework configuration
├── .env.example          # Environment variables template
└── README.md             # This file
```

## Prerequisites

1. **AWS Account** with appropriate permissions
2. **AWS Cognito User Pool** configured
3. **Python 3.9+**
4. **Node.js** (for Serverless Framework)

## Setup

### 1. Clone and Install Dependencies

```bash
# Install Python dependencies
pip install -r requirements.txt

# Install Serverless Framework (if not already installed)
npm install -g serverless
npm install serverless-python-requirements
```

### 2. Configure AWS Cognito

Create an AWS Cognito User Pool with the following settings:

1. **User Pool**: Create a new user pool
2. **App Client**: Create an app client with:
   - Enable username/password authentication
   - Generate client secret (optional but recommended)
   - Enable SRP authentication flows
3. **Note down**:
   - User Pool ID
   - App Client ID
   - App Client Secret (if generated)
   - AWS Region

### 3. Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your Cognito configuration
nano .env
```

Required environment variables:
```env
AWS_REGION=us-east-1
COGNITO_USER_POOL_ID=your_user_pool_id_here
COGNITO_CLIENT_ID=your_client_id_here
COGNITO_CLIENT_SECRET=your_client_secret_here
```

## Running Locally

```bash
# Start the development server
python main.py

# Or using uvicorn directly
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

The API will be available at:
- **API**: http://localhost:8000
- **Documentation**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## API Endpoints

### Authentication Endpoints

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| POST | `/auth/login` | User login | None |
| POST | `/auth/refresh` | Refresh access token | None |
| POST | `/auth/logout` | User logout | Required |
| GET | `/auth/me` | Get current user info | Required |
| POST | `/auth/validate` | Validate token | Required |

### Protected Endpoints

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET | `/protected/profile` | Get user profile | Required |
| GET | `/protected/dashboard` | Get user dashboard | Required |
| GET | `/protected/public-with-optional-auth` | Public with optional auth | Optional |
| GET | `/protected/admin` | Admin dashboard | Required |

### Public Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/` | API information |
| GET | `/health` | Health check |
| GET | `/ping` | Ping endpoint |

## Usage Examples

### 1. User Login

```bash
curl -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "your_username",
    "password": "your_password"
  }'
```

Response:
```json
{
  "access_token": "eyJhbGciOiJSUzI1NiIs...",
  "token_type": "bearer",
  "expires_in": 3600,
  "refresh_token": "eyJjdHkiOiJKV1QiLCJlbmMi..."
}
```

### 2. Access Protected Route

```bash
curl -X GET "http://localhost:8000/protected/profile" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 3. Refresh Token

```bash
curl -X POST "http://localhost:8000/auth/refresh" \
  -H "Content-Type: application/json" \
  -d '{
    "refresh_token": "YOUR_REFRESH_TOKEN"
  }'
```

## Deployment to AWS

### Using Serverless Framework

1. **Configure AWS credentials**:
```bash
aws configure
# or
export AWS_ACCESS_KEY_ID=your_access_key
export AWS_SECRET_ACCESS_KEY=your_secret_key
```

2. **Set environment variables**:
```bash
export COGNITO_USER_POOL_ID=your_user_pool_id
export COGNITO_CLIENT_ID=your_client_id
export COGNITO_CLIENT_SECRET=your_client_secret
```

3. **Deploy**:
```bash
# Deploy to dev stage
serverless deploy

# Deploy to production
serverless deploy --stage prod
```

4. **Get deployment info**:
```bash
serverless info
```

## Testing

Create test users in your Cognito User Pool and test the authentication flow:

1. **Create a test user** in AWS Cognito Console
2. **Set a temporary password** and force password change on first login
3. **Test login** using the API endpoints
4. **Test protected routes** with the received JWT token

## Security Considerations

1. **Environment Variables**: Never commit `.env` files to version control
2. **HTTPS**: Always use HTTPS in production
3. **Token Storage**: Store JWT tokens securely on the client side
4. **Token Expiration**: Implement proper token refresh logic
5. **CORS**: Configure CORS properly for your frontend domains
6. **Rate Limiting**: Consider implementing rate limiting for authentication endpoints

## Troubleshooting

### Common Issues

1. **Invalid JWT Token**: Check that your Cognito configuration is correct
2. **CORS Errors**: Verify CORS settings in `config.py`
3. **Authentication Failures**: Ensure user exists and password is correct
4. **Deployment Issues**: Check AWS credentials and permissions

### Debug Mode

Enable debug mode by setting `DEBUG=true` in your `.env` file for detailed error messages.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
