import boto3
import hmac
import hashlib
import base64
import json
import requests
from typing import Op<PERSON>, Dict, Any
from jose import jwt, JWTError
from botocore.exceptions import ClientError
from config import settings


class CognitoAuth:
    def __init__(self):
        self.client = boto3.client('cognito-idp', region_name=settings.aws_region)
        self.user_pool_id = settings.cognito_user_pool_id
        self.client_id = settings.cognito_client_id
        self.client_secret = settings.cognito_client_secret
        self._jwks = None
        
    def _get_secret_hash(self, username: str) -> Optional[str]:
        """Generate secret hash for Cognito client if client secret is provided"""
        if not self.client_secret:
            return None
        
        message = username + self.client_id
        dig = hmac.new(
            str(self.client_secret).encode('utf-8'),
            msg=str(message).encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()
        return base64.b64encode(dig).decode()
    
    def authenticate_user(self, username: str, password: str) -> Dict[str, Any]:
        """Authenticate user with Cognito"""
        try:
            auth_params = {
                'USERNAME': username,
                'PASSWORD': password
            }
            
            # Add secret hash if client secret is configured
            secret_hash = self._get_secret_hash(username)
            if secret_hash:
                auth_params['SECRET_HASH'] = secret_hash
            
            response = self.client.admin_initiate_auth(
                UserPoolId=self.user_pool_id,
                ClientId=self.client_id,
                AuthFlow='ADMIN_NO_SRP_AUTH',
                AuthParameters=auth_params
            )
            
            return {
                'success': True,
                'tokens': response['AuthenticationResult'],
                'challenge': response.get('ChallengeName'),
                'session': response.get('Session')
            }
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            
            return {
                'success': False,
                'error_code': error_code,
                'error_message': error_message
            }
    
    def refresh_token(self, refresh_token: str, username: str) -> Dict[str, Any]:
        """Refresh access token using refresh token"""
        try:
            auth_params = {
                'REFRESH_TOKEN': refresh_token
            }
            
            # Add secret hash if client secret is configured
            secret_hash = self._get_secret_hash(username)
            if secret_hash:
                auth_params['SECRET_HASH'] = secret_hash
            
            response = self.client.admin_initiate_auth(
                UserPoolId=self.user_pool_id,
                ClientId=self.client_id,
                AuthFlow='REFRESH_TOKEN_AUTH',
                AuthParameters=auth_params
            )
            
            return {
                'success': True,
                'tokens': response['AuthenticationResult']
            }
            
        except ClientError as e:
            return {
                'success': False,
                'error_code': e.response['Error']['Code'],
                'error_message': e.response['Error']['Message']
            }
    
    def logout_user(self, access_token: str) -> Dict[str, Any]:
        """Logout user by invalidating tokens"""
        try:
            self.client.global_sign_out(
                AccessToken=access_token
            )
            return {'success': True}
            
        except ClientError as e:
            return {
                'success': False,
                'error_code': e.response['Error']['Code'],
                'error_message': e.response['Error']['Message']
            }
    
    def get_user_info(self, access_token: str) -> Dict[str, Any]:
        """Get user information from access token"""
        try:
            response = self.client.get_user(AccessToken=access_token)

            # Parse user attributes
            attributes = {}
            for attr in response.get('UserAttributes', []):
                attributes[attr['Name']] = attr['Value']

            return {
                'success': True,
                'username': response['Username'],
                'attributes': attributes,
                'user_status': response.get('UserStatus'),
                'mfa_options': response.get('MFAOptions', [])
            }

        except ClientError as e:
            return {
                'success': False,
                'error_code': e.response['Error']['Code'],
                'error_message': e.response['Error']['Message']
            }

    def get_jwks(self) -> Dict[str, Any]:
        """Get JSON Web Key Set from Cognito"""
        if self._jwks is None:
            jwks_url = f"https://cognito-idp.{settings.aws_region}.amazonaws.com/{self.user_pool_id}/.well-known/jwks.json"
            response = requests.get(jwks_url)
            self._jwks = response.json()
        return self._jwks

    def verify_jwt_token(self, token: str) -> Dict[str, Any]:
        """Verify JWT token and extract claims"""
        try:
            # Get the key ID from token header
            unverified_header = jwt.get_unverified_header(token)
            kid = unverified_header['kid']

            # Get the public key
            jwks = self.get_jwks()
            key = None
            for jwk in jwks['keys']:
                if jwk['kid'] == kid:
                    key = jwk
                    break

            if not key:
                return {
                    'valid': False,
                    'error': 'Public key not found'
                }

            # Verify the token
            claims = jwt.decode(
                token,
                key,
                algorithms=[settings.jwt_algorithm],
                audience=settings.jwt_audience or settings.cognito_client_id,
                issuer=f"https://cognito-idp.{settings.aws_region}.amazonaws.com/{self.user_pool_id}"
            )

            return {
                'valid': True,
                'claims': claims,
                'user_id': claims.get('sub'),
                'username': claims.get('cognito:username'),
                'email': claims.get('email')
            }

        except JWTError as e:
            return {
                'valid': False,
                'error': str(e)
            }


# Global instance
cognito_auth = CognitoAuth()
