from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional
from auth.cognito import cognito_auth
from models.user import UserProfile


security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> UserProfile:
    """
    Dependency to get current authenticated user from JWT token
    """
    token = credentials.credentials
    
    # Verify the JWT token
    verification_result = cognito_auth.verify_jwt_token(token)
    
    if not verification_result['valid']:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Invalid token: {verification_result.get('error', 'Unknown error')}",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    claims = verification_result['claims']
    
    # Extract user information from claims
    user_profile = UserProfile(
        user_id=claims.get('sub'),
        username=claims.get('cognito:username'),
        email=claims.get('email'),
        first_name=claims.get('given_name'),
        last_name=claims.get('family_name'),
        phone_number=claims.get('phone_number')
    )
    
    return user_profile


async def get_optional_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[UserProfile]:
    """
    Dependency to get current user if token is provided, otherwise return None
    """
    if not credentials:
        return None
    
    try:
        return await get_current_user(credentials)
    except HTTPException:
        return None


async def verify_token_only(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> dict:
    """
    Dependency that only verifies the token and returns claims
    """
    token = credentials.credentials
    
    verification_result = cognito_auth.verify_jwt_token(token)
    
    if not verification_result['valid']:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Invalid token: {verification_result.get('error', 'Unknown error')}",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return verification_result['claims']
