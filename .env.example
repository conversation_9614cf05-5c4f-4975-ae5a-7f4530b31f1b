# AWS Cognito Configuration
AWS_REGION=us-east-1
COGNITO_USER_POOL_ID=your_user_pool_id_here
COGNITO_CLIENT_ID=your_client_id_here
COGNITO_CLIENT_SECRET=your_client_secret_here

# JWT Configuration (optional - will be auto-discovered from Cognito)
JWT_ALGORITHM=RS256
JWT_AUDIENCE=your_client_id_here

# API Configuration
API_TITLE=FastAPI with Cognito Auth
API_VERSION=1.0.0
DEBUG=false

# CORS Configuration
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8000"]
ALLOWED_METHODS=["GET", "POST", "PUT", "DELETE"]
ALLOWED_HEADERS=["*"]
